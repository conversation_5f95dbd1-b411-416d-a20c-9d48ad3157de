load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "cache.go",
        "helpers.go",
        "lightclient.go",
        "store.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/light-client",
    visibility = ["//visibility:public"],
    deps = [
        "//async/event:go_default_library",
        "//beacon-chain/core/feed:go_default_library",
        "//beacon-chain/core/feed/state:go_default_library",
        "//beacon-chain/db/iface:go_default_library",
        "//beacon-chain/execution:go_default_library",
        "//beacon-chain/p2p:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/light-client:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/ssz:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "cache_test.go",
        "lightclient_test.go",
        "store_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//async/event:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/db/testing:go_default_library",
        "//beacon-chain/p2p/testing:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/light-client:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/ssz:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
    ],
)
