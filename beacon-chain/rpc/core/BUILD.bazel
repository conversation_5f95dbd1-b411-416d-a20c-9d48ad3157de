load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "beacon.go",
        "errors.go",
        "log.go",
        "service.go",
        "validator.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/rpc/core",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/blockchain:go_default_library",
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/epoch/precompute:go_default_library",
        "//beacon-chain/core/feed:go_default_library",
        "//beacon-chain/core/feed/operation:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/core/transition:go_default_library",
        "//beacon-chain/core/validators:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/forkchoice/types:go_default_library",
        "//beacon-chain/operations/synccommittee:go_default_library",
        "//beacon-chain/p2p:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/stategen:go_default_library",
        "//beacon-chain/sync:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//consensus-types/validator:go_default_library",
        "//crypto/bls:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//time:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_x_sync//errgroup:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = ["validator_test.go"],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/blockchain/testing:go_default_library",
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/p2p/testing:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//consensus-types/validator:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
    ],
)
