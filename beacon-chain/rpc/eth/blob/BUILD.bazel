load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "handlers.go",
        "server.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/rpc/eth/blob",
    visibility = ["//visibility:public"],
    deps = [
        "//api:go_default_library",
        "//api/server/structs:go_default_library",
        "//beacon-chain/blockchain:go_default_library",
        "//beacon-chain/rpc/core:go_default_library",
        "//beacon-chain/rpc/eth/shared:go_default_library",
        "//beacon-chain/rpc/lookup:go_default_library",
        "//beacon-chain/rpc/options:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//network/httputil:go_default_library",
        "//runtime/version:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = ["handlers_test.go"],
    embed = [":go_default_library"],
    deps = [
        "//api:go_default_library",
        "//api/server/structs:go_default_library",
        "//beacon-chain/blockchain/kzg:go_default_library",
        "//beacon-chain/blockchain/testing:go_default_library",
        "//beacon-chain/db/filesystem:go_default_library",
        "//beacon-chain/db/testing:go_default_library",
        "//beacon-chain/rpc/lookup:go_default_library",
        "//beacon-chain/rpc/testutil:go_default_library",
        "//beacon-chain/verification:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//network/httputil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
    ],
)
