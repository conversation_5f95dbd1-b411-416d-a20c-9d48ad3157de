load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = ["handlers.go"],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/rpc/eth/config",
    visibility = ["//visibility:public"],
    deps = [
        "//api/server/structs:go_default_library",
        "//config/params:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//network/httputil:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = ["handlers_test.go"],
    embed = [":go_default_library"],
    deps = [
        "//api/server/structs:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
    ],
)
