load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "handlers.go",
        "handlers_pool.go",
        "handlers_state.go",
        "handlers_validator.go",
        "log.go",
        "metrics.go",
        "server.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/rpc/eth/beacon",
    visibility = ["//visibility:public"],
    deps = [
        "//api:go_default_library",
        "//api/server:go_default_library",
        "//api/server/structs:go_default_library",
        "//beacon-chain/blockchain:go_default_library",
        "//beacon-chain/blockchain/kzg:go_default_library",
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/cache/depositsnapshot:go_default_library",
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/blocks:go_default_library",
        "//beacon-chain/core/feed:go_default_library",
        "//beacon-chain/core/feed/block:go_default_library",
        "//beacon-chain/core/feed/operation:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/transition:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/db/filters:go_default_library",
        "//beacon-chain/execution:go_default_library",
        "//beacon-chain/operations/attestations:go_default_library",
        "//beacon-chain/operations/blstoexec:go_default_library",
        "//beacon-chain/operations/slashings:go_default_library",
        "//beacon-chain/operations/voluntaryexits:go_default_library",
        "//beacon-chain/p2p:go_default_library",
        "//beacon-chain/rpc/core:go_default_library",
        "//beacon-chain/rpc/eth/helpers:go_default_library",
        "//beacon-chain/rpc/eth/shared:go_default_library",
        "//beacon-chain/rpc/lookup:go_default_library",
        "//beacon-chain/rpc/prysm/v1alpha1/validator:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//beacon-chain/state/stategen:go_default_library",
        "//beacon-chain/sync:go_default_library",
        "//config/features:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//consensus-types/validator:go_default_library",
        "//container/multi-value-slice:go_default_library",
        "//crypto/bls:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//network/httputil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_prysmaticlabs_fastssz//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "handlers_equivocation_test.go",
        "handlers_pool_test.go",
        "handlers_state_test.go",
        "handlers_test.go",
        "handlers_validators_test.go",
        "init_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//api:go_default_library",
        "//api/server:go_default_library",
        "//api/server/structs:go_default_library",
        "//beacon-chain/blockchain/kzg:go_default_library",
        "//beacon-chain/blockchain/testing:go_default_library",
        "//beacon-chain/cache/depositsnapshot:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/core/transition:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/db/testing:go_default_library",
        "//beacon-chain/forkchoice/doubly-linked-tree:go_default_library",
        "//beacon-chain/operations/attestations:go_default_library",
        "//beacon-chain/operations/blstoexec:go_default_library",
        "//beacon-chain/operations/blstoexec/mock:go_default_library",
        "//beacon-chain/operations/slashings/mock:go_default_library",
        "//beacon-chain/operations/synccommittee:go_default_library",
        "//beacon-chain/operations/voluntaryexits/mock:go_default_library",
        "//beacon-chain/p2p/testing:go_default_library",
        "//beacon-chain/rpc/core:go_default_library",
        "//beacon-chain/rpc/eth/shared/testing:go_default_library",
        "//beacon-chain/rpc/lookup:go_default_library",
        "//beacon-chain/rpc/testutil:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//beacon-chain/sync/initial-sync/testing:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls:go_default_library",
        "//crypto/bls/common:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//encoding/ssz:go_default_library",
        "//network/httputil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/mock:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prysmaticlabs_fastssz//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//hooks/test:go_default_library",
        "@com_github_stretchr_testify//mock:go_default_library",
        "@org_uber_go_mock//gomock:go_default_library",
    ],
)
