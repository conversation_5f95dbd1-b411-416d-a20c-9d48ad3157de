load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "doc.go",
        "metrics.go",
        "process_attestation.go",
        "process_block.go",
        "process_exit.go",
        "process_sync_committee.go",
        "service.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/monitor",
    visibility = ["//beacon-chain:__subpackages__"],
    deps = [
        "//async/event:go_default_library",
        "//beacon-chain/blockchain:go_default_library",
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/blocks:go_default_library",
        "//beacon-chain/core/feed:go_default_library",
        "//beacon-chain/core/feed/operation:go_default_library",
        "//beacon-chain/core/feed/state:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/stategen:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "process_attestation_test.go",
        "process_block_test.go",
        "process_exit_test.go",
        "process_sync_committee_test.go",
        "service_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/blockchain/testing:go_default_library",
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/feed:go_default_library",
        "//beacon-chain/core/feed/state:go_default_library",
        "//beacon-chain/db/testing:go_default_library",
        "//beacon-chain/forkchoice/doubly-linked-tree:go_default_library",
        "//beacon-chain/state/stategen:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_sirupsen_logrus//hooks/test:go_default_library",
    ],
)
