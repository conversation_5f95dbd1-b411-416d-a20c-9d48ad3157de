load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "object_mapping.go",
        "rpc_errors.go",
        "rpc_goodbye_codes.go",
        "types.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/p2p/types",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//cmd:__subpackages__",
        "//slasher/rpc:__pkg__",
        "//testing/util:__pkg__",
        "//validator/client:__pkg__",
    ],
    deps = [
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/light-client:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//consensus-types/wrapper:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/metadata:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prysmaticlabs_fastssz//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "object_mapping_test.go",
        "types_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "@com_github_prysmaticlabs_fastssz//:go_default_library",
    ],
)
