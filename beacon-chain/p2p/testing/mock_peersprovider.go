package testing

import (
	"context"
	"sync"

	"github.com/OffchainLabs/prysm/v6/beacon-chain/p2p/peers"
	"github.com/OffchainLabs/prysm/v6/beacon-chain/p2p/peers/scorers"
	pb "github.com/OffchainLabs/prysm/v6/proto/prysm/v1alpha1"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/p2p/enode"
	"github.com/ethereum/go-ethereum/p2p/enr"
	"github.com/libp2p/go-libp2p/core/network"
	"github.com/libp2p/go-libp2p/core/peer"
	ma "github.com/multiformats/go-multiaddr"
	log "github.com/sirupsen/logrus"
)

const (
	MockRawPeerId0 = "*****************************************************"
	MockRawPeerId1 = "*****************************************************"
)

// MockPeersProvider implements PeersProvider for testing.
type MockPeersProvider struct {
	lock  sync.Mutex
	peers *peers.Status
}

// ClearPeers removes all known peers.
func (m *MockPeersProvider) ClearPeers() {
	m.lock.Lock()
	defer m.lock.Unlock()
	m.peers = peers.NewStatus(context.Background(), &peers.StatusConfig{
		PeerLimit: 30,
		ScorerParams: &scorers.Config{
			BadResponsesScorerConfig: &scorers.BadResponsesScorerConfig{
				Threshold: 5,
			},
		},
	})
}

// Peers provides access the peer status.
func (m *MockPeersProvider) Peers() *peers.Status {
	m.lock.Lock()
	defer m.lock.Unlock()
	if m.peers == nil {
		m.peers = peers.NewStatus(context.Background(), &peers.StatusConfig{
			PeerLimit: 30,
			ScorerParams: &scorers.Config{
				BadResponsesScorerConfig: &scorers.BadResponsesScorerConfig{
					Threshold: 5,
				},
			},
		})
		// Pretend we are connected to two peers
		id0, err := peer.Decode(MockRawPeerId0)
		if err != nil {
			log.WithError(err).Debug("Cannot decode")
		}
		ma0, err := ma.NewMultiaddr("/ip4/***************/tcp/13000")
		if err != nil {
			log.WithError(err).Debug("Cannot decode")
		}
		m.peers.Add(createENR(), id0, ma0, network.DirInbound)
		m.peers.SetConnectionState(id0, peers.Connected)
		m.peers.SetChainState(id0, &pb.StatusV2{FinalizedEpoch: 10})
		id1, err := peer.Decode(MockRawPeerId1)
		if err != nil {
			log.WithError(err).Debug("Cannot decode")
		}
		ma1, err := ma.NewMultiaddr("/ip4/************/tcp/30000/ipfs/QmfAgkmjiZNZhr2wFN9TwaRgHouMTBT6HELyzE5A3BT2wK/p2p-circuit")
		if err != nil {
			log.WithError(err).Debug("Cannot decode")
		}
		m.peers.Add(createENR(), id1, ma1, network.DirOutbound)
		m.peers.SetConnectionState(id1, peers.Connected)
		m.peers.SetChainState(id1, &pb.StatusV2{FinalizedEpoch: 11})
	}
	return m.peers
}

func createENR() *enr.Record {
	key, err := crypto.GenerateKey()
	if err != nil {
		log.Error(err)
	}
	db, err := enode.OpenDB("")
	if err != nil {
		log.Error("Could not open node's peer database")
	}
	lNode := enode.NewLocalNode(db, key)
	return lNode.Node().Record()
}
