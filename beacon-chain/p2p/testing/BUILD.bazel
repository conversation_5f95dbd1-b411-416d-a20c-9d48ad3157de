load("@prysm//tools/go:def.bzl", "go_library")

go_library(
    name = "go_default_library",
    testonly = True,
    srcs = [
        "fuzz_p2p.go",
        "mock_broadcaster.go",
        "mock_host.go",
        "mock_listener.go",
        "mock_metadataprovider.go",
        "mock_peermanager.go",
        "mock_peersprovider.go",
        "p2p.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/p2p/testing",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//testing:__subpackages__",
    ],
    deps = [
        "//beacon-chain/core/peerdas:go_default_library",
        "//beacon-chain/p2p/encoder:go_default_library",
        "//beacon-chain/p2p/peers:go_default_library",
        "//beacon-chain/p2p/peers/scorers:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/metadata:go_default_library",
        "//testing/require:go_default_library",
        "@com_github_ethereum_go_ethereum//crypto:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enode:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enr:go_default_library",
        "@com_github_libp2p_go_libp2p//:go_default_library",
        "@com_github_libp2p_go_libp2p//config:go_default_library",
        "@com_github_libp2p_go_libp2p//core:go_default_library",
        "@com_github_libp2p_go_libp2p//core/connmgr:go_default_library",
        "@com_github_libp2p_go_libp2p//core/control:go_default_library",
        "@com_github_libp2p_go_libp2p//core/event:go_default_library",
        "@com_github_libp2p_go_libp2p//core/host:go_default_library",
        "@com_github_libp2p_go_libp2p//core/network:go_default_library",
        "@com_github_libp2p_go_libp2p//core/peer:go_default_library",
        "@com_github_libp2p_go_libp2p//core/peerstore:go_default_library",
        "@com_github_libp2p_go_libp2p//core/protocol:go_default_library",
        "@com_github_libp2p_go_libp2p//p2p/transport/tcp:go_default_library",
        "@com_github_libp2p_go_libp2p_pubsub//:go_default_library",
        "@com_github_multiformats_go_multiaddr//:go_default_library",
        "@com_github_prysmaticlabs_fastssz//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
