/*
Package p2p implements the Ethereum consensus networking specification.

Canonical spec reference: https://github.com/ethereum/consensus-specs/blob/master/specs/phase0/p2p-interface.md

Prysm specific implementation design docs
  - Networking Design Doc: https://docs.google.com/document/d/1VyhobQRkEjEkEPxmmdWvaHfKWn0j6dEae_wLZlrFtfU/view

This package is heavily utilizes the libp2p go implementation by Protocol Labs.
*/
package p2p
