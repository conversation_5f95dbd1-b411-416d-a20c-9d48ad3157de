load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "assigner.go",
        "log.go",
        "status.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/p2p/peers",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//cmd:__subpackages__",
    ],
    deps = [
        "//beacon-chain/forkchoice/types:go_default_library",
        "//beacon-chain/p2p/peers/peerdata:go_default_library",
        "//beacon-chain/p2p/peers/scorers:go_default_library",
        "//cmd/beacon-chain/flags:go_default_library",
        "//config/features:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/rand:go_default_library",
        "//math:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/metadata:go_default_library",
        "//time:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enr:go_default_library",
        "@com_github_libp2p_go_libp2p//core/network:go_default_library",
        "@com_github_libp2p_go_libp2p//core/peer:go_default_library",
        "@com_github_multiformats_go_multiaddr//:go_default_library",
        "@com_github_multiformats_go_multiaddr//net:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "assigner_test.go",
        "benchmark_test.go",
        "peers_test.go",
        "status_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/p2p/peers/peerdata:go_default_library",
        "//beacon-chain/p2p/peers/scorers:go_default_library",
        "//cmd/beacon-chain/flags:go_default_library",
        "//config/features:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//consensus-types/wrapper:go_default_library",
        "//proto/eth/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enr:go_default_library",
        "@com_github_libp2p_go_libp2p//core/network:go_default_library",
        "@com_github_libp2p_go_libp2p//core/peer:go_default_library",
        "@com_github_multiformats_go_multiaddr//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)
