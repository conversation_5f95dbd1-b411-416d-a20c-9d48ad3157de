load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "bad_responses.go",
        "block_providers.go",
        "gossip_scorer.go",
        "peer_status.go",
        "service.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/p2p/peers/scorers",
    visibility = ["//beacon-chain:__subpackages__"],
    deps = [
        "//beacon-chain/p2p/peers/peerdata:go_default_library",
        "//beacon-chain/p2p/types:go_default_library",
        "//cmd/beacon-chain/flags:go_default_library",
        "//config/features:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/rand:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "@com_github_libp2p_go_libp2p//core/peer:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "bad_responses_test.go",
        "block_providers_test.go",
        "gossip_scorer_test.go",
        "peer_status_test.go",
        "scorers_test.go",
        "service_test.go",
    ],
    deps = [
        ":go_default_library",
        "//beacon-chain/p2p/peers:go_default_library",
        "//beacon-chain/p2p/peers/peerdata:go_default_library",
        "//beacon-chain/p2p/types:go_default_library",
        "//cmd/beacon-chain/flags:go_default_library",
        "//config/features:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/rand:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//time:go_default_library",
        "@com_github_libp2p_go_libp2p//core/network:go_default_library",
        "@com_github_libp2p_go_libp2p//core/peer:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)
