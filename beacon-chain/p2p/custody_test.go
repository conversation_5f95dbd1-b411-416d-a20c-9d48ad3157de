package p2p

import (
	"context"
	"strings"
	"testing"

	"github.com/OffchainLabs/prysm/v6/beacon-chain/core/peerdas"
	"github.com/OffchainLabs/prysm/v6/beacon-chain/p2p/peers"
	"github.com/OffchainLabs/prysm/v6/beacon-chain/p2p/peers/scorers"
	testp2p "github.com/OffchainLabs/prysm/v6/beacon-chain/p2p/testing"
	"github.com/OffchainLabs/prysm/v6/config/params"
	"github.com/OffchainLabs/prysm/v6/consensus-types/primitives"
	"github.com/OffchainLabs/prysm/v6/consensus-types/wrapper"
	pb "github.com/OffchainLabs/prysm/v6/proto/prysm/v1alpha1"
	"github.com/OffchainLabs/prysm/v6/proto/prysm/v1alpha1/metadata"
	"github.com/OffchainLabs/prysm/v6/testing/require"
	"github.com/ethereum/go-ethereum/p2p/enode"
	"github.com/ethereum/go-ethereum/p2p/enr"
	"github.com/libp2p/go-libp2p/core/network"
)

func TestEarliestAvailableSlot(t *testing.T) {
	t.Run("No custody info available", func(t *testing.T) {
		service := &Service{
			custodyInfo: nil,
		}

		_, err := service.EarliestAvailableSlot()

		require.NotNil(t, err)
	})

	t.Run("Valid custody info", func(t *testing.T) {
		const expected primitives.Slot = 100

		service := &Service{
			custodyInfo: &custodyInfo{
				earliestAvailableSlot: expected,
			},
		}

		slot, err := service.EarliestAvailableSlot()

		require.NoError(t, err)
		require.Equal(t, expected, slot)
	})
}

func TestCustodyGroupCount(t *testing.T) {
	t.Run("No custody info available", func(t *testing.T) {
		service := &Service{
			custodyInfo: nil,
		}

		_, err := service.CustodyGroupCount()

		require.NotNil(t, err)
		require.Equal(t, true, strings.Contains(err.Error(), "no custody info available"))
	})

	t.Run("Valid custody info", func(t *testing.T) {
		const expected uint64 = 5

		service := &Service{
			custodyInfo: &custodyInfo{
				groupCount: expected,
			},
		}

		count, err := service.CustodyGroupCount()

		require.NoError(t, err)
		require.Equal(t, expected, count)
	})
}

func TestUpdateCustodyInfo(t *testing.T) {
	params.SetupTestConfigCleanup(t)
	config := params.BeaconConfig()
	config.SamplesPerSlot = 8
	config.FuluForkEpoch = 10
	params.OverrideBeaconConfig(config)

	testCases := []struct {
		name               string
		initialCustodyInfo *custodyInfo
		inputSlot          primitives.Slot
		inputGroupCount    uint64
		expectedUpdated    bool
		expectedSlot       primitives.Slot
		expectedGroupCount uint64
		expectedErr        string
	}{
		{
			name:               "First time setting custody info",
			initialCustodyInfo: nil,
			inputSlot:          100,
			inputGroupCount:    5,
			expectedUpdated:    true,
			expectedSlot:       100,
			expectedGroupCount: 5,
		},
		{
			name: "Group count decrease - no update",
			initialCustodyInfo: &custodyInfo{
				earliestAvailableSlot: 50,
				groupCount:            10,
			},
			inputSlot:          60,
			inputGroupCount:    8,
			expectedUpdated:    false,
			expectedSlot:       50,
			expectedGroupCount: 10,
		},
		{
			name: "Earliest slot decrease - error",
			initialCustodyInfo: &custodyInfo{
				earliestAvailableSlot: 100,
				groupCount:            5,
			},
			inputSlot:       50,
			inputGroupCount: 10,
			expectedErr:     "earliest available slot 50 is less than the current one 100",
		},
		{
			name: "Group count increase but <= samples per slot",
			initialCustodyInfo: &custodyInfo{
				earliestAvailableSlot: 50,
				groupCount:            5,
			},
			inputSlot:          60,
			inputGroupCount:    8,
			expectedUpdated:    true,
			expectedSlot:       50,
			expectedGroupCount: 8,
		},
		{
			name: "Group count increase > samples per slot, before Fulu fork",
			initialCustodyInfo: &custodyInfo{
				earliestAvailableSlot: 50,
				groupCount:            5,
			},
			inputSlot:          60,
			inputGroupCount:    15,
			expectedUpdated:    true,
			expectedSlot:       50,
			expectedGroupCount: 15,
		},
		{
			name: "Group count increase > samples per slot, after Fulu fork",
			initialCustodyInfo: &custodyInfo{
				earliestAvailableSlot: 50,
				groupCount:            5,
			},
			inputSlot:          500,
			inputGroupCount:    15,
			expectedUpdated:    true,
			expectedSlot:       500,
			expectedGroupCount: 15,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			service := &Service{
				custodyInfo: tc.initialCustodyInfo,
			}

			slot, groupCount, err := service.UpdateCustodyInfo(tc.inputSlot, tc.inputGroupCount)

			if tc.expectedErr != "" {
				require.NotNil(t, err)
				require.Equal(t, true, strings.Contains(err.Error(), tc.expectedErr))
				return
			}

			require.NoError(t, err)
			require.Equal(t, tc.expectedSlot, slot)
			require.Equal(t, tc.expectedGroupCount, groupCount)

			if tc.expectedUpdated {
				require.NotNil(t, service.custodyInfo)
				require.Equal(t, tc.expectedSlot, service.custodyInfo.earliestAvailableSlot)
				require.Equal(t, tc.expectedGroupCount, service.custodyInfo.groupCount)
			}
		})
	}
}

func TestCustodyGroupCountFromPeer(t *testing.T) {
	const (
		expectedENR      uint64 = 7
		expectedMetadata uint64 = 8
		pid                     = "test-id"
	)

	cgc := peerdas.Cgc(expectedENR)

	// Define a nil record
	var nilRecord *enr.Record = nil

	// Define an empty record (record with non `cgc` entry)
	emptyRecord := &enr.Record{}

	// Define a nominal record
	nominalRecord := &enr.Record{}
	nominalRecord.Set(cgc)

	// Define a metadata with zero custody.
	zeroMetadata := wrapper.WrappedMetadataV2(&pb.MetaDataV2{
		CustodyGroupCount: 0,
	})

	// Define a nominal metadata.
	nominalMetadata := wrapper.WrappedMetadataV2(&pb.MetaDataV2{
		CustodyGroupCount: expectedMetadata,
	})

	testCases := []struct {
		name     string
		record   *enr.Record
		metadata metadata.Metadata
		expected uint64
	}{
		{
			name:     "No metadata - No ENR",
			record:   nilRecord,
			expected: params.BeaconConfig().CustodyRequirement,
		},
		{
			name:     "No metadata - Empty ENR",
			record:   emptyRecord,
			expected: params.BeaconConfig().CustodyRequirement,
		},
		{
			name:     "No Metadata - ENR",
			record:   nominalRecord,
			expected: expectedENR,
		},
		{
			name:     "Metadata with 0 value - ENR",
			record:   nominalRecord,
			metadata: zeroMetadata,
			expected: expectedENR,
		},
		{
			name:     "Metadata - ENR",
			record:   nominalRecord,
			metadata: nominalMetadata,
			expected: expectedMetadata,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create peers status.
			peers := peers.NewStatus(t.Context(), &peers.StatusConfig{
				ScorerParams: &scorers.Config{},
			})

			// Set the metadata.
			if tc.metadata != nil {
				peers.SetMetadata(pid, tc.metadata)
			}

			// Add a new peer with the record.
			peers.Add(tc.record, pid, nil, network.DirOutbound)

			// Create a new service.
			service := &Service{
				peers:    peers,
				metaData: tc.metadata,
				host:     testp2p.NewTestP2P(t).Host(),
			}

			// Retrieve the custody count from the remote peer.
			actual := service.CustodyGroupCountFromPeer(pid)

			// Verify the result.
			require.Equal(t, tc.expected, actual)
		})
	}

}

func TestCustodyGroupCountFromPeerENR(t *testing.T) {
	const (
		expectedENR uint64 = 7
		pid                = "test-id"
	)

	cgc := peerdas.Cgc(expectedENR)
	custodyRequirement := params.BeaconConfig().CustodyRequirement

	testCases := []struct {
		name     string
		record   *enr.Record
		expected uint64
		wantErr  bool
	}{
		{
			name:     "No ENR record",
			record:   nil,
			expected: custodyRequirement,
		},
		{
			name:     "Empty ENR record",
			record:   &enr.Record{},
			expected: custodyRequirement,
		},
		{
			name: "Valid ENR with custody group count",
			record: func() *enr.Record {
				record := &enr.Record{}
				record.Set(cgc)
				return record
			}(),
			expected: expectedENR,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			peers := peers.NewStatus(context.Background(), &peers.StatusConfig{
				ScorerParams: &scorers.Config{},
			})

			if tc.record != nil {
				peers.Add(tc.record, pid, nil, network.DirOutbound)
			}

			service := &Service{
				peers: peers,
				host:  testp2p.NewTestP2P(t).Host(),
			}

			actual := service.custodyGroupCountFromPeerENR(pid)
			require.Equal(t, tc.expected, actual)
		})
	}
}

// TestENRCustodyGroupCountManipulationVulnerability demonstrates how malicious peers
// can over-claim custody group count in ENR to get preferential selection for PeerDAS
// data column subnets, then under-serve or selectively withhold columns.
func TestENRCustodyGroupCountManipulationVulnerability(t *testing.T) {
	ctx := context.Background()

	// Test 1: Demonstrate that ENR custody group count directly influences peer selection
	t.Run("ENR custody group count influences peer discovery selection", func(t *testing.T) {
		// Create two peers with different ENR custody group counts
		honestPeerCGC := uint64(2)  // Honest peer with modest custody
		sybilPeerCGC := uint64(10)  // Sybil peer claiming high custody

		// Create ENR records for both peers
		honestRecord := &enr.Record{}
		honestRecord.Set(peerdas.Cgc(honestPeerCGC))

		sybilRecord := &enr.Record{}
		sybilRecord.Set(peerdas.Cgc(sybilPeerCGC))

		// Create mock nodes with different node IDs
		honestNodeID := enode.ID{0x01} // Simple node ID for honest peer
		sybilNodeID := enode.ID{0x02}  // Simple node ID for sybil peer

		// Compute data column subnets for both peers using their ENR values
		honestSubnets, err := dataColumnSubnets(honestNodeID, honestRecord)
		require.NoError(t, err)

		sybilSubnets, err := dataColumnSubnets(sybilNodeID, sybilRecord)
		require.NoError(t, err)

		// VULNERABILITY: Sybil peer appears to cover more subnets due to higher ENR CGC
		require.Equal(t, true, len(sybilSubnets) > len(honestSubnets),
			"Sybil peer with higher ENR CGC should appear to cover more subnets")

		t.Logf("Honest peer (CGC=%d) covers %d subnets: %v", honestPeerCGC, len(honestSubnets), getSubnetList(honestSubnets))
		t.Logf("Sybil peer (CGC=%d) covers %d subnets: %v", sybilPeerCGC, len(sybilSubnets), getSubnetList(sybilSubnets))

		// This means the sybil peer is more likely to be selected for discovery
		// when looking for peers that cover specific data column subnets
	})

	// Test 2: Demonstrate ENR fallback when metadata is missing/zero
	t.Run("ENR fallback allows manipulation when metadata missing", func(t *testing.T) {
		const (
			peerID = "sybil-peer"
			maliciousENRCGC = uint64(15) // Very high custody claim in ENR
		)

		// Create a malicious ENR record
		maliciousRecord := &enr.Record{}
		maliciousRecord.Set(peerdas.Cgc(maliciousENRCGC))

		// Create peers status without metadata (simulating missing metadata)
		peers := peers.NewStatus(ctx, &peers.StatusConfig{
			ScorerParams: &scorers.Config{},
		})

		// Add peer with malicious ENR but no metadata
		peers.Add(maliciousRecord, peerID, nil, network.DirOutbound)

		// Create service
		service := &Service{
			peers: peers,
			host:  testp2p.NewTestP2P(t).Host(),
		}

		// VULNERABILITY: Service trusts the ENR value when metadata is missing
		actualCGC := service.CustodyGroupCountFromPeer(peerID)
		require.Equal(t, maliciousENRCGC, actualCGC,
			"Service should trust malicious ENR value when metadata is missing")

		// This means the peer will be considered for many more columns than it actually serves
		t.Logf("Malicious peer claims CGC=%d in ENR, service believes it", actualCGC)
	})

	// Test 3: Demonstrate ENR fallback when metadata is zero
	t.Run("ENR fallback allows manipulation when metadata is zero", func(t *testing.T) {
		const (
			peerID = "sybil-peer-zero"
			maliciousENRCGC = uint64(12)
		)

		// Create malicious ENR and zero metadata
		maliciousRecord := &enr.Record{}
		maliciousRecord.Set(peerdas.Cgc(maliciousENRCGC))

		zeroMetadata := wrapper.WrappedMetadataV2(&pb.MetaDataV2{
			CustodyGroupCount: 0, // Zero metadata triggers ENR fallback
		})

		peers := peers.NewStatus(ctx, &peers.StatusConfig{
			ScorerParams: &scorers.Config{},
		})

		// Set zero metadata and malicious ENR
		peers.SetMetadata(peerID, zeroMetadata)
		peers.Add(maliciousRecord, peerID, nil, network.DirOutbound)

		service := &Service{
			peers: peers,
			host:  testp2p.NewTestP2P(t).Host(),
		}

		// VULNERABILITY: Service falls back to ENR when metadata is zero
		actualCGC := service.CustodyGroupCountFromPeer(peerID)
		require.Equal(t, maliciousENRCGC, actualCGC,
			"Service should fall back to ENR when metadata is zero")

		t.Logf("Peer has zero metadata but claims CGC=%d in ENR, service believes ENR", actualCGC)
	})

	// Test 4: Demonstrate the attack scenario - preferential selection
	t.Run("ATTACK: Sybil fleet gets preferential selection", func(t *testing.T) {
		// Simulate a scenario where we need peers for specific data column subnets
		neededSubnets := map[uint64]int{
			1:  1, // Need 1 peer for subnet 1
			17: 1, // Need 1 peer for subnet 17
			42: 1, // Need 1 peer for subnet 42
		}

		// Create honest peers with realistic custody
		honestPeers := make(map[enode.ID]*enr.Record)
		for i := 0; i < 5; i++ {
			nodeID := enode.ID{byte(i + 10)}
			record := &enr.Record{}
			record.Set(peerdas.Cgc(uint64(2))) // Honest custody count
			honestPeers[nodeID] = record
		}

		// Create sybil peers with inflated custody claims
		sybilPeers := make(map[enode.ID]*enr.Record)
		for i := 0; i < 3; i++ {
			nodeID := enode.ID{byte(i + 20)}
			record := &enr.Record{}
			record.Set(peerdas.Cgc(uint64(20))) // Inflated custody claim
			sybilPeers[nodeID] = record
		}

		// Count how many subnets each peer type can serve
		honestCoverage := make(map[uint64]int) // subnet -> peer count
		sybilCoverage := make(map[uint64]int)

		for nodeID, record := range honestPeers {
			subnets, err := dataColumnSubnets(nodeID, record)
			require.NoError(t, err)
			for subnet := range subnets {
				if _, needed := neededSubnets[subnet]; needed {
					honestCoverage[subnet]++
				}
			}
		}

		for nodeID, record := range sybilPeers {
			subnets, err := dataColumnSubnets(nodeID, record)
			require.NoError(t, err)
			for subnet := range subnets {
				if _, needed := neededSubnets[subnet]; needed {
					sybilCoverage[subnet]++
				}
			}
		}

		// VULNERABILITY: Sybil peers appear to cover more needed subnets
		for subnet := range neededSubnets {
			t.Logf("Subnet %d: Honest peers=%d, Sybil peers=%d",
				subnet, honestCoverage[subnet], sybilCoverage[subnet])

			// In a real discovery scenario, sybil peers would be more likely to be selected
			// because they appear to cover more subnets due to their inflated ENR claims
		}

		// The attack succeeds because:
		// 1. Discovery uses ENR CGC to compute which subnets a peer covers
		// 2. Higher CGC = more subnets covered = higher selection probability
		// 3. Sybil peers can then selectively withhold data or serve invalid data
		// 4. Downscoring only happens AFTER failed requests, wasting time and resources
	})
}

// Helper function to convert subnet map to sorted slice for logging
func getSubnetList(subnets map[uint64]bool) []uint64 {
	var list []uint64
	for subnet := range subnets {
		list = append(list, subnet)
	}
	// Simple sort for consistent output
	for i := 0; i < len(list)-1; i++ {
		for j := i + 1; j < len(list); j++ {
			if list[i] > list[j] {
				list[i], list[j] = list[j], list[i]
			}
		}
	}
	return list
}
