load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "addr_factory.go",
        "broadcaster.go",
        "config.go",
        "connection_gater.go",
        "custody.go",
        "dial_relay_node.go",
        "discovery.go",
        "doc.go",
        "fork.go",
        "fork_watcher.go",
        "gossip_scoring_params.go",
        "gossip_topic_mappings.go",
        "handshake.go",
        "info.go",
        "interfaces.go",
        "log.go",
        "message_id.go",
        "monitoring.go",
        "options.go",
        "pubsub.go",
        "pubsub_filter.go",
        "pubsub_tracer.go",
        "rpc_topic_mappings.go",
        "sender.go",
        "service.go",
        "subnets.go",
        "topics.go",
        "utils.go",
        "watch_peers.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/p2p",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//cmd:__subpackages__",
        "//testing/endtoend/evaluators:__pkg__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//async:go_default_library",
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/feed/state:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/peerdas:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/db/kv:go_default_library",
        "//beacon-chain/p2p/encoder:go_default_library",
        "//beacon-chain/p2p/peers:go_default_library",
        "//beacon-chain/p2p/peers/peerdata:go_default_library",
        "//beacon-chain/p2p/peers/scorers:go_default_library",
        "//beacon-chain/p2p/types:go_default_library",
        "//beacon-chain/startup:go_default_library",
        "//cmd/beacon-chain/flags:go_default_library",
        "//config/features:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//consensus-types/wrapper:go_default_library",
        "//container/leaky-bucket:go_default_library",
        "//crypto/ecdsa:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//io/file:go_default_library",
        "//math:go_default_library",
        "//monitoring/tracing:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//network:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/metadata:go_default_library",
        "//runtime:go_default_library",
        "//runtime/version:go_default_library",
        "//time:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_btcsuite_btcd_btcec_v2//:go_default_library",
        "@com_github_ethereum_go_ethereum//crypto:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/discover:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enode:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enr:go_default_library",
        "@com_github_holiman_uint256//:go_default_library",
        "@com_github_kr_pretty//:go_default_library",
        "@com_github_libp2p_go_libp2p//:go_default_library",
        "@com_github_libp2p_go_libp2p//config:go_default_library",
        "@com_github_libp2p_go_libp2p//core/connmgr:go_default_library",
        "@com_github_libp2p_go_libp2p//core/control:go_default_library",
        "@com_github_libp2p_go_libp2p//core/crypto:go_default_library",
        "@com_github_libp2p_go_libp2p//core/host:go_default_library",
        "@com_github_libp2p_go_libp2p//core/network:go_default_library",
        "@com_github_libp2p_go_libp2p//core/peer:go_default_library",
        "@com_github_libp2p_go_libp2p//core/peerstore:go_default_library",
        "@com_github_libp2p_go_libp2p//core/protocol:go_default_library",
        "@com_github_libp2p_go_libp2p//p2p/net/connmgr:go_default_library",
        "@com_github_libp2p_go_libp2p//p2p/security/noise:go_default_library",
        "@com_github_libp2p_go_libp2p//p2p/transport/quic:go_default_library",
        "@com_github_libp2p_go_libp2p//p2p/transport/tcp:go_default_library",
        "@com_github_libp2p_go_libp2p_mplex//:go_default_library",
        "@com_github_libp2p_go_libp2p_pubsub//:go_default_library",
        "@com_github_libp2p_go_libp2p_pubsub//pb:go_default_library",
        "@com_github_libp2p_go_mplex//:go_default_library",
        "@com_github_multiformats_go_multiaddr//:go_default_library",
        "@com_github_multiformats_go_multiaddr//net:go_default_library",
        "@com_github_patrickmn_go_cache//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_prysmaticlabs_fastssz//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "addr_factory_test.go",
        "broadcaster_test.go",
        "connection_gater_test.go",
        "custody_test.go",
        "dial_relay_node_test.go",
        "discovery_test.go",
        "fork_test.go",
        "gossip_scoring_params_test.go",
        "gossip_topic_mappings_test.go",
        "message_id_test.go",
        "options_test.go",
        "parameter_test.go",
        "pubsub_filter_test.go",
        "pubsub_fuzz_test.go",
        "pubsub_test.go",
        "rpc_topic_mappings_test.go",
        "sender_test.go",
        "service_test.go",
        "subnets_test.go",
        "utils_test.go",
    ],
    embed = [":go_default_library"],
    flaky = True,
    tags = ["requires-network"],
    deps = [
        "//beacon-chain/blockchain/kzg:go_default_library",
        "//beacon-chain/blockchain/testing:go_default_library",
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/peerdas:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/db/testing:go_default_library",
        "//beacon-chain/p2p/encoder:go_default_library",
        "//beacon-chain/p2p/peers:go_default_library",
        "//beacon-chain/p2p/peers/peerdata:go_default_library",
        "//beacon-chain/p2p/peers/scorers:go_default_library",
        "//beacon-chain/p2p/testing:go_default_library",
        "//beacon-chain/p2p/types:go_default_library",
        "//beacon-chain/startup:go_default_library",
        "//cmd/beacon-chain/flags:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//consensus-types/wrapper:go_default_library",
        "//container/leaky-bucket:go_default_library",
        "//crypto/ecdsa:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//network:go_default_library",
        "//proto/eth/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/metadata:go_default_library",
        "//proto/testing:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_ethereum_go_ethereum//crypto:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/discover:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enode:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enr:go_default_library",
        "@com_github_golang_snappy//:go_default_library",
        "@com_github_libp2p_go_libp2p//:go_default_library",
        "@com_github_libp2p_go_libp2p//core/connmgr:go_default_library",
        "@com_github_libp2p_go_libp2p//core/crypto:go_default_library",
        "@com_github_libp2p_go_libp2p//core/host:go_default_library",
        "@com_github_libp2p_go_libp2p//core/network:go_default_library",
        "@com_github_libp2p_go_libp2p//core/peer:go_default_library",
        "@com_github_libp2p_go_libp2p//core/protocol:go_default_library",
        "@com_github_libp2p_go_libp2p//p2p/security/noise:go_default_library",
        "@com_github_libp2p_go_libp2p_pubsub//:go_default_library",
        "@com_github_libp2p_go_libp2p_pubsub//pb:go_default_library",
        "@com_github_multiformats_go_multiaddr//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//hooks/test:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
