load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "block_cache.go",
        "block_reader.go",
        "deposit.go",
        "engine_client.go",
        "errors.go",
        "log.go",
        "log_processing.go",
        "metrics.go",
        "options.go",
        "payload_body.go",
        "prometheus.go",
        "rpc_connection.go",
        "service.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/execution",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//cmd/beacon-chain:__subpackages__",
        "//contracts:__subpackages__",
        "//testing/spectest:__subpackages__",
    ],
    deps = [
        "//beacon-chain/blockchain/kzg:go_default_library",
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/cache/depositsnapshot:go_default_library",
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/feed:go_default_library",
        "//beacon-chain/core/feed/state:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/peerdas:go_default_library",
        "//beacon-chain/core/transition:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/execution/types:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//beacon-chain/state/stategen:go_default_library",
        "//beacon-chain/verification:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/payload-attribute:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/trie:go_default_library",
        "//contracts/deposit:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//io/logs:go_default_library",
        "//monitoring/clientstats:go_default_library",
        "//monitoring/tracing:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//network:go_default_library",
        "//network/authorization:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//time:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//:go_default_library",
        "@com_github_ethereum_go_ethereum//accounts/abi/bind:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_ethereum_go_ethereum//core/types:go_default_library",
        "@com_github_ethereum_go_ethereum//ethclient:go_default_library",
        "@com_github_ethereum_go_ethereum//rpc:go_default_library",
        "@com_github_holiman_uint256//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@io_k8s_client_go//tools/cache:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    size = "medium",
    srcs = [
        "block_cache_test.go",
        "block_reader_test.go",
        "deposit_test.go",
        "engine_client_fuzz_test.go",
        "engine_client_test.go",
        "execution_chain_test.go",
        "init_test.go",
        "log_processing_test.go",
        "mock_test.go",
        "payload_body_test.go",
        "prometheus_test.go",
        "service_test.go",
    ],
    data = glob(["testdata/**"]),
    embed = [":go_default_library"],
    deps = [
        "//async/event:go_default_library",
        "//beacon-chain/blockchain/kzg:go_default_library",
        "//beacon-chain/cache/depositsnapshot:go_default_library",
        "//beacon-chain/core/feed:go_default_library",
        "//beacon-chain/core/feed/state:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/peerdas:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/db/filesystem:go_default_library",
        "//beacon-chain/db/testing:go_default_library",
        "//beacon-chain/execution/testing:go_default_library",
        "//beacon-chain/execution/types:go_default_library",
        "//beacon-chain/forkchoice:go_default_library",
        "//beacon-chain/forkchoice/doubly-linked-tree:go_default_library",
        "//beacon-chain/startup:go_default_library",
        "//beacon-chain/state/stategen:go_default_library",
        "//beacon-chain/verification:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/payload-attribute:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/trie:go_default_library",
        "//contracts/deposit:go_default_library",
        "//contracts/deposit/mock:go_default_library",
        "//crypto/bls:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//genesis:go_default_library",
        "//monitoring/clientstats:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//:go_default_library",
        "@com_github_ethereum_go_ethereum//beacon/engine:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_ethereum_go_ethereum//core/types:go_default_library",
        "@com_github_ethereum_go_ethereum//ethclient/simulated:go_default_library",
        "@com_github_ethereum_go_ethereum//rpc:go_default_library",
        "@com_github_holiman_uint256//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_sirupsen_logrus//hooks/test:go_default_library",
    ],
)
