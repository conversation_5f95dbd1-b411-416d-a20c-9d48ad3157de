load("@prysm//tools/go:def.bzl", "go_library")

go_library(
    name = "go_default_library",
    testonly = True,
    srcs = [
        "mock_engine_client.go",
        "mock_execution_chain.go",
        "mock_faulty_powchain.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/execution/testing",
    visibility = [
        "//visibility:public",
    ],
    deps = [
        "//async/event:go_default_library",
        "//beacon-chain/core/peerdas:go_default_library",
        "//beacon-chain/execution/types:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/payload-attribute:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_ethereum_go_ethereum//core/types:go_default_library",
        "@com_github_ethereum_go_ethereum//ethclient/simulated:go_default_library",
        "@com_github_ethereum_go_ethereum//rpc:go_default_library",
        "@com_github_holiman_uint256//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
    ],
)
