load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "log.go",
        "metric.go",
        "option.go",
        "service.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/builder",
    visibility = ["//visibility:public"],
    deps = [
        "//api/client/builder:go_default_library",
        "//beacon-chain/blockchain:go_default_library",
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//cmd/beacon-chain/flags:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//monitoring/tracing:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_urfave_cli_v2//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = ["service_test.go"],
    embed = [":go_default_library"],
    deps = [
        "//api/client/builder/testing:go_default_library",
        "//beacon-chain/blockchain/testing:go_default_library",
        "//beacon-chain/db/testing:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
    ],
)
