load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "log.go",
        "p2p.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/node/registration",
    visibility = ["//beacon-chain/node:__subpackages__"],
    deps = [
        "//cmd:go_default_library",
        "//config/params:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_urfave_cli_v2//:go_default_library",
        "@in_gopkg_yaml_v2//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = ["p2p_test.go"],
    embed = [":go_default_library"],
    deps = [
        "//cmd:go_default_library",
        "//config/params:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "@com_github_urfave_cli_v2//:go_default_library",
    ],
)
