load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "clear_db.go",
        "config.go",
        "log.go",
        "node.go",
        "options.go",
        "prometheus.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/node",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//cmd/beacon-chain:__subpackages__",
    ],
    deps = [
        "//api/server/httprest:go_default_library",
        "//api/server/middleware:go_default_library",
        "//async/event:go_default_library",
        "//beacon-chain/blockchain:go_default_library",
        "//beacon-chain/builder:go_default_library",
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/cache/depositsnapshot:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/db/filesystem:go_default_library",
        "//beacon-chain/db/kv:go_default_library",
        "//beacon-chain/db/pruner:go_default_library",
        "//beacon-chain/db/slasherkv:go_default_library",
        "//beacon-chain/execution:go_default_library",
        "//beacon-chain/forkchoice:go_default_library",
        "//beacon-chain/forkchoice/doubly-linked-tree:go_default_library",
        "//beacon-chain/light-client:go_default_library",
        "//beacon-chain/monitor:go_default_library",
        "//beacon-chain/node/registration:go_default_library",
        "//beacon-chain/operations/attestations:go_default_library",
        "//beacon-chain/operations/blstoexec:go_default_library",
        "//beacon-chain/operations/slashings:go_default_library",
        "//beacon-chain/operations/synccommittee:go_default_library",
        "//beacon-chain/operations/voluntaryexits:go_default_library",
        "//beacon-chain/p2p:go_default_library",
        "//beacon-chain/p2p/peers:go_default_library",
        "//beacon-chain/rpc:go_default_library",
        "//beacon-chain/slasher:go_default_library",
        "//beacon-chain/startup:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/stategen:go_default_library",
        "//beacon-chain/sync:go_default_library",
        "//beacon-chain/sync/backfill:go_default_library",
        "//beacon-chain/sync/backfill/coverage:go_default_library",
        "//beacon-chain/sync/checkpoint:go_default_library",
        "//beacon-chain/sync/initial-sync:go_default_library",
        "//beacon-chain/verification:go_default_library",
        "//cmd:go_default_library",
        "//cmd/beacon-chain/flags:go_default_library",
        "//config/features:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/slice:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//genesis:go_default_library",
        "//monitoring/prometheus:go_default_library",
        "//monitoring/tracing:go_default_library",
        "//runtime:go_default_library",
        "//runtime/prereqs:go_default_library",
        "//runtime/version:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_urfave_cli_v2//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    size = "small",
    srcs = [
        "config_test.go",
        "node_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//api/server/middleware:go_default_library",
        "//beacon-chain/blockchain:go_default_library",
        "//beacon-chain/builder:go_default_library",
        "//beacon-chain/core/feed/state:go_default_library",
        "//beacon-chain/db/filesystem:go_default_library",
        "//beacon-chain/execution:go_default_library",
        "//beacon-chain/execution/testing:go_default_library",
        "//beacon-chain/monitor:go_default_library",
        "//cmd:go_default_library",
        "//cmd/beacon-chain/flags:go_default_library",
        "//config/features:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//runtime:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_sirupsen_logrus//hooks/test:go_default_library",
        "@com_github_urfave_cli_v2//:go_default_library",
    ],
)
