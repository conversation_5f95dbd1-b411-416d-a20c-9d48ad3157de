load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "kv.go",
        "log.go",
        "metrics.go",
        "migrate.go",
        "pruning.go",
        "schema.go",
        "slasher.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/db/slasherkv",
    visibility = ["//beacon-chain:__subpackages__"],
    deps = [
        "//beacon-chain/db/iface:go_default_library",
        "//beacon-chain/db/kv:go_default_library",
        "//beacon-chain/slasher/types:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//io/file:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_golang_snappy//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_prysmaticlabs_fastssz//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@io_etcd_go_bbolt//:go_default_library",
        "@org_golang_x_sync//errgroup:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "kv_test.go",
        "migrate_test.go",
        "pruning_test.go",
        "slasher_test.go",
        "slasherkv_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/slasher/types:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/require:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_prysmaticlabs_fastssz//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_sirupsen_logrus//hooks/test:go_default_library",
        "@io_etcd_go_bbolt//:go_default_library",
    ],
)
