load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = ["pruner.go"],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/db/pruner",
    visibility = [
        "//beacon-chain:__subpackages__",
    ],
    deps = [
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/db/iface:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = ["pruner_test.go"],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/db/testing:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots/testing:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_sirupsen_logrus//hooks/test:go_default_library",
    ],
)
