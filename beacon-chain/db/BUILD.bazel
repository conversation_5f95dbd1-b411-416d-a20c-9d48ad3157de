load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "alias.go",
        "db.go",
        "errors.go",
        "log.go",
        "restore.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/db",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//cmd/beacon-chain:__subpackages__",
        "//genesis:__subpackages__",
        "//testing/slasher/simulator:__pkg__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//beacon-chain/db/iface:go_default_library",
        "//beacon-chain/db/kv:go_default_library",
        "//cmd:go_default_library",
        "//io/file:go_default_library",
        "//io/prompt:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_urfave_cli_v2//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "db_test.go",
        "restore_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/db/kv:go_default_library",
        "//cmd:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "@com_github_sirupsen_logrus//hooks/test:go_default_library",
        "@com_github_urfave_cli_v2//:go_default_library",
    ],
)
