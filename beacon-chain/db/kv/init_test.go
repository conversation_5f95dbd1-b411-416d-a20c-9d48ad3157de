package kv

import (
	"io"
	"os"
	"testing"

	"github.com/OffchainLabs/prysm/v6/config/params"
	"github.com/sirupsen/logrus"
)

func init() {
	// Override network name so that hardcoded genesis files are not loaded.
	if err := params.SetActive(params.MainnetTestConfig()); err != nil {
		panic(err)
	}
}

func TestMain(m *testing.M) {
	logrus.SetLevel(logrus.DebugLevel)
	logrus.SetOutput(io.Discard)
	os.Exit(m.Run())
}
