load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "archived_point.go",
        "backfill.go",
        "backup.go",
        "blocks.go",
        "checkpoint.go",
        "custody.go",
        "deposit_contract.go",
        "encoding.go",
        "error.go",
        "execution_chain.go",
        "finalized_block_roots.go",
        "genesis.go",
        "key.go",
        "kv.go",
        "lightclient.go",
        "log.go",
        "migration.go",
        "migration_archived_index.go",
        "migration_block_slot_index.go",
        "migration_finalized_parent.go",
        "migration_state_validators.go",
        "p2p.go",
        "schema.go",
        "state.go",
        "state_summary.go",
        "state_summary_cache.go",
        "utils.go",
        "validated_checkpoint.go",
        "wss.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/db/kv",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/core/blocks:go_default_library",
        "//beacon-chain/db/filters:go_default_library",
        "//beacon-chain/db/iface:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/features:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/light-client:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/slice:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//encoding/ssz/detect:go_default_library",
        "//genesis:go_default_library",
        "//io/file:go_default_library",
        "//monitoring/progress:go_default_library",
        "//monitoring/tracing:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/dbval:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//time:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_dgraph_io_ristretto_v2//:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_golang_snappy//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_prysmaticlabs_fastssz//:go_default_library",
        "@com_github_prysmaticlabs_prombbolt//:go_default_library",
        "@com_github_schollz_progressbar_v3//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@io_etcd_go_bbolt//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "archived_point_test.go",
        "backfill_test.go",
        "backup_test.go",
        "blocks_test.go",
        "checkpoint_test.go",
        "custody_test.go",
        "deposit_contract_test.go",
        "encoding_test.go",
        "execution_chain_test.go",
        "finalized_block_roots_test.go",
        "genesis_test.go",
        "init_test.go",
        "kv_test.go",
        "lightclient_test.go",
        "migration_archived_index_test.go",
        "migration_block_slot_index_test.go",
        "migration_state_validators_test.go",
        "p2p_test.go",
        "state_summary_test.go",
        "state_test.go",
        "utils_test.go",
        "validated_checkpoint_test.go",
        "wss_test.go",
    ],
    data = glob(["testdata/**"]),
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/db/filters:go_default_library",
        "//beacon-chain/db/iface:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/features:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/light-client:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//genesis:go_default_library",
        "//proto/dbval:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/testing:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_golang_snappy//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@io_bazel_rules_go//go/tools/bazel:go_default_library",
        "@io_etcd_go_bbolt//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
