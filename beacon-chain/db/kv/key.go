package kv

import "bytes"

// In order for an encoding to be Altair compatible, it must be prefixed with altair key.
func hasAltairKey(enc []byte) bool {
	if len(altair<PERSON>ey) >= len(enc) {
		return false
	}
	return bytes.Equal(enc[:len(altair<PERSON><PERSON>)], altair<PERSON><PERSON>)
}

func hasBellatrixKey(enc []byte) bool {
	if len(bellatrixKey) >= len(enc) {
		return false
	}
	return bytes.Equal(enc[:len(bellatrixKey)], bellatrixKey)
}

func hasBellatrixBlindKey(enc []byte) bool {
	if len(bellatrixBlindKey) >= len(enc) {
		return false
	}
	return bytes.Equal(enc[:len(bellatrixBlindKey)], bellatrixBlindKey)
}

func hasCapellaKey(enc []byte) bool {
	if len(capella<PERSON>ey) >= len(enc) {
		return false
	}
	return bytes.Equal(enc[:len(capella<PERSON><PERSON>)], capella<PERSON>ey)
}

func hasCapellaBlind<PERSON>ey(enc []byte) bool {
	if len(capellaBlindKey) >= len(enc) {
		return false
	}
	return bytes.Equal(enc[:len(capellaBlindKey)], capellaBlindKey)
}

func hasDenebKey(enc []byte) bool {
	if len(denebKey) >= len(enc) {
		return false
	}
	return bytes.Equal(enc[:len(denebKey)], denebKey)
}

func hasDenebBlindKey(enc []byte) bool {
	if len(denebBlindKey) >= len(enc) {
		return false
	}
	return bytes.Equal(enc[:len(denebBlindKey)], denebBlindKey)
}

// HasElectraKey verifies if the encoding is Electra compatible.
func HasElectraKey(enc []byte) bool {
	if len(ElectraKey) >= len(enc) {
		return false
	}
	return bytes.Equal(enc[:len(ElectraKey)], ElectraKey)
}

func hasElectraBlindKey(enc []byte) bool {
	if len(electraBlindKey) >= len(enc) {
		return false
	}
	return bytes.Equal(enc[:len(electraBlindKey)], electraBlindKey)
}

func hasFuluKey(enc []byte) bool {
	if len(fuluKey) >= len(enc) {
		return false
	}
	return bytes.Equal(enc[:len(fuluKey)], fuluKey)
}

func hasFuluBlindKey(enc []byte) bool {
	if len(fuluBlindKey) >= len(enc) {
		return false
	}
	return bytes.Equal(enc[:len(fuluBlindKey)], fuluBlindKey)
}
