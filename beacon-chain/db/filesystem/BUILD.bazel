load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "blob.go",
        "cache.go",
        "data_column.go",
        "data_column_cache.go",
        "doc.go",
        "iteration.go",
        "layout.go",
        "layout_by_epoch.go",
        "layout_flat.go",
        "log.go",
        "metrics.go",
        "mock.go",
        "pruner.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/db/filesystem",
    visibility = ["//visibility:public"],
    deps = [
        "//async:go_default_library",
        "//async/event:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/verification:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//io/file:go_default_library",
        "//runtime/logging:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_spf13_afero//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "blob_test.go",
        "cache_test.go",
        "data_column_cache_test.go",
        "data_column_test.go",
        "iteration_test.go",
        "layout_test.go",
        "migration_test.go",
        "pruner_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/verification:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_prysmaticlabs_fastssz//:go_default_library",
        "@com_github_spf13_afero//:go_default_library",
    ],
)
