load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "errors.go",
        "filter.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/db/filters",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//consensus-types/primitives:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = ["filter_test.go"],
    embed = [":go_default_library"],
    deps = [
        "//consensus-types/primitives:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
    ],
)
