load("@prysm//tools/go:def.bzl", "go_library")

go_library(
    name = "go_default_library",
    testonly = True,
    srcs = ["mock.go"],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/blockchain/testing",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//testing:__subpackages__",
    ],
    deps = [
        "//async/event:go_default_library",
        "//beacon-chain/core/epoch/precompute:go_default_library",
        "//beacon-chain/core/feed:go_default_library",
        "//beacon-chain/core/feed/block:go_default_library",
        "//beacon-chain/core/feed/operation:go_default_library",
        "//beacon-chain/core/feed/state:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/das:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/forkchoice:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/forkchoice:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)
