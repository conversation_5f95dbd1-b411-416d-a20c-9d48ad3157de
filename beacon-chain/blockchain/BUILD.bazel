load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "chain_info.go",
        "chain_info_forkchoice.go",
        "currently_syncing_block.go",
        "defragment.go",
        "error.go",
        "execution_engine.go",
        "forkchoice_update_execution.go",
        "head.go",
        "head_sync_committee_info.go",
        "init_sync_process_block.go",
        "log.go",
        "merge_ascii_art.go",
        "metrics.go",
        "options.go",
        "pow_block.go",
        "process_attestation.go",
        "process_attestation_helpers.go",
        "process_block.go",
        "process_block_helpers.go",
        "receive_attestation.go",
        "receive_blob.go",
        "receive_block.go",
        "receive_data_column.go",
        "service.go",
        "setup_forkchoice.go",
        "tracked_proposer.go",
        "weak_subjectivity_checks.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/blockchain",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//cmd/beacon-chain:__subpackages__",
        "//testing/slasher/simulator:__pkg__",
        "//testing/spectest:__subpackages__",
    ],
    deps = [
        "//async:go_default_library",
        "//async/event:go_default_library",
        "//beacon-chain/blockchain/kzg:go_default_library",
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/blocks:go_default_library",
        "//beacon-chain/core/electra:go_default_library",
        "//beacon-chain/core/epoch/precompute:go_default_library",
        "//beacon-chain/core/feed:go_default_library",
        "//beacon-chain/core/feed/state:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/peerdas:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/core/transition:go_default_library",
        "//beacon-chain/das:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/db/filesystem:go_default_library",
        "//beacon-chain/db/filters:go_default_library",
        "//beacon-chain/execution:go_default_library",
        "//beacon-chain/forkchoice:go_default_library",
        "//beacon-chain/forkchoice/doubly-linked-tree:go_default_library",
        "//beacon-chain/forkchoice/types:go_default_library",
        "//beacon-chain/light-client:go_default_library",
        "//beacon-chain/operations/attestations:go_default_library",
        "//beacon-chain/operations/blstoexec:go_default_library",
        "//beacon-chain/operations/slashings:go_default_library",
        "//beacon-chain/operations/voluntaryexits:go_default_library",
        "//beacon-chain/p2p:go_default_library",
        "//beacon-chain/slasher/types:go_default_library",
        "//beacon-chain/startup:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/stategen:go_default_library",
        "//beacon-chain/verification:go_default_library",
        "//cmd/beacon-chain/flags:go_default_library",
        "//config/features:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/forkchoice:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/payload-attribute:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//math:go_default_library",
        "//monitoring/tracing:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/eth/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//runtime/version:go_default_library",
        "//time:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_holiman_uint256//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@org_golang_x_sync//errgroup:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    size = "medium",
    srcs = [
        "blockchain_test.go",
        "chain_info_norace_test.go",
        "chain_info_test.go",
        "checktags_test.go",
        "error_test.go",
        "execution_engine_test.go",
        "forkchoice_update_execution_test.go",
        "head_sync_committee_info_test.go",
        "head_test.go",
        "init_sync_process_block_test.go",
        "init_test.go",
        "log_test.go",
        "metrics_test.go",
        "mock_test.go",
        "pow_block_test.go",
        "process_attestation_test.go",
        "process_block_test.go",
        "receive_attestation_test.go",
        "receive_block_test.go",
        "service_norace_test.go",
        "service_test.go",
        "setup_forkchoice_test.go",
        "setup_test.go",
        "weak_subjectivity_checks_test.go",
    ],
    embed = [":go_default_library"],
    gotags = ["develop"],
    tags = ["CI_race_detection"],
    deps = [
        "//async/event:go_default_library",
        "//beacon-chain/blockchain/testing:go_default_library",
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/cache/depositsnapshot:go_default_library",
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/blocks:go_default_library",
        "//beacon-chain/core/feed/state:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/peerdas:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/core/transition:go_default_library",
        "//beacon-chain/das:go_default_library",
        "//beacon-chain/db:go_default_library",
        "//beacon-chain/db/filesystem:go_default_library",
        "//beacon-chain/db/testing:go_default_library",
        "//beacon-chain/execution:go_default_library",
        "//beacon-chain/execution/testing:go_default_library",
        "//beacon-chain/forkchoice:go_default_library",
        "//beacon-chain/forkchoice/doubly-linked-tree:go_default_library",
        "//beacon-chain/forkchoice/types:go_default_library",
        "//beacon-chain/light-client:go_default_library",
        "//beacon-chain/operations/attestations:go_default_library",
        "//beacon-chain/operations/attestations/kv:go_default_library",
        "//beacon-chain/operations/blstoexec:go_default_library",
        "//beacon-chain/operations/slashings:go_default_library",
        "//beacon-chain/operations/voluntaryexits:go_default_library",
        "//beacon-chain/p2p:go_default_library",
        "//beacon-chain/p2p/testing:go_default_library",
        "//beacon-chain/startup:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//beacon-chain/state/stategen:go_default_library",
        "//beacon-chain/verification:go_default_library",
        "//config/features:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/trie:go_default_library",
        "//crypto/bls:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//genesis:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/eth/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_ethereum_go_ethereum//core/types:go_default_library",
        "@com_github_holiman_uint256//:go_default_library",
        "@com_github_libp2p_go_libp2p//core/peer:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_sirupsen_logrus//hooks/test:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
