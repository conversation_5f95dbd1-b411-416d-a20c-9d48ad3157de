load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "attestation.go",
        "justification_finalization.go",
        "new.go",
        "reward_penalty.go",
        "slashing.go",
        "type.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/core/epoch/precompute",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//testing/spectest:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//math:go_default_library",
        "//monitoring/tracing:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "attestation_test.go",
        "justification_finalization_test.go",
        "new_test.go",
        "precompute_test.go",
        "reward_penalty_test.go",
        "slashing_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//math:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
