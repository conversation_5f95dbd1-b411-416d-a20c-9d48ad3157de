load("@prysm//tools/go:def.bzl", "go_library")

go_library(
    name = "go_default_library",
    srcs = [
        "events.go",
        "notifier.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/core/feed/state",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//testing/slasher/simulator:__subpackages__",
    ],
    deps = [
        "//async/event:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
    ],
)
