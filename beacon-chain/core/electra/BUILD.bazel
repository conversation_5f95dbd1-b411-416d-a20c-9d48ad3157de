load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "attestation.go",
        "churn.go",
        "consolidations.go",
        "deposits.go",
        "effective_balance_updates.go",
        "error.go",
        "registry_updates.go",
        "transition.go",
        "transition_no_verify_sig.go",
        "upgrade.go",
        "validator.go",
        "withdrawals.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/core/electra",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/blocks:go_default_library",
        "//beacon-chain/core/epoch:go_default_library",
        "//beacon-chain/core/epoch/precompute:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/core/validators:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//beacon-chain/state/state-native/custom-types:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//contracts/deposit:go_default_library",
        "//crypto/bls/common:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//math:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_ethereum_go_ethereum//common/math:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "churn_test.go",
        "consolidations_test.go",
        "deposit_fuzz_test.go",
        "deposits_test.go",
        "effective_balance_updates_test.go",
        "error_test.go",
        "export_test.go",
        "registry_updates_test.go",
        "transition_no_verify_sig_test.go",
        "transition_test.go",
        "upgrade_test.go",
        "validator_test.go",
        "withdrawals_test.go",
    ],
    data = glob(["testdata/**"]),
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//beacon-chain/state/testing:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/fuzz:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_google_gofuzz//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_sirupsen_logrus//hooks/test:go_default_library",
    ],
)
