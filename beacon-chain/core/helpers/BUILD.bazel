load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "attestation.go",
        "beacon_committee.go",
        "block.go",
        "genesis.go",
        "legacy.go",
        "metrics.go",
        "randao.go",
        "rewards_penalties.go",
        "shuffle.go",
        "sync_committee.go",
        "validator_churn.go",
        "validators.go",
        "weak_subjectivity.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/core/helpers",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/forkchoice/types:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/slice:go_default_library",
        "//container/trie:go_default_library",
        "//crypto/bls:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//math:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    size = "medium",
    srcs = [
        "attestation_test.go",
        "beacon_committee_test.go",
        "block_test.go",
        "legacy_test.go",
        "private_access_fuzz_noop_test.go",  # keep
        "private_access_test.go",
        "randao_test.go",
        "rewards_penalties_test.go",
        "shuffle_test.go",
        "sync_committee_test.go",
        "validator_churn_test.go",
        "validators_test.go",
        "weak_subjectivity_test.go",
    ],
    data = glob(["testdata/**"]),
    embed = [":go_default_library"],
    shard_count = 2,
    tags = ["CI_race_detection"],
    deps = [
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/forkchoice/types:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/slice:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_stretchr_testify//require:go_default_library",
    ],
)
