load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "domain.go",
        "signing_root.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/core/signing",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/state:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prysmaticlabs_fastssz//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "domain_test.go",
        "signing_root_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_google_gofuzz//:go_default_library",
    ],
)
