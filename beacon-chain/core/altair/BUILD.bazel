load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "attestation.go",
        "block.go",
        "deposit.go",
        "epoch_precompute.go",
        "epoch_spec.go",
        "reward.go",
        "sync_committee.go",
        "transition.go",
        "upgrade.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/core/altair",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/core/blocks:go_default_library",
        "//beacon-chain/core/epoch:go_default_library",
        "//beacon-chain/core/epoch/precompute:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/p2p/types:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//math:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "attestation_test.go",
        "block_test.go",
        "deposit_fuzz_test.go",
        "deposit_test.go",
        "epoch_precompute_test.go",
        "epoch_spec_test.go",
        "exports_test.go",
        "reward_test.go",
        "sync_committee_test.go",
        "transition_test.go",
        "upgrade_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/core/epoch:go_default_library",
        "//beacon-chain/core/epoch/precompute:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/p2p/types:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/trie:go_default_library",
        "//crypto/bls:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//math:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/fuzz:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_google_gofuzz//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
