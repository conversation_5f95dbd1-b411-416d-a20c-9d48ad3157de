load("@prysm//tools/go:def.bzl", "go_library")

go_library(
    name = "go_default_library",
    srcs = [
        "log.go",
        "write_block_to_disk.go",
        "write_state_to_disk.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/core/transition/interop",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//beacon-chain/state:go_default_library",
        "//config/features:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//io/file:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)
