load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "log.go",
        "skip_slot_cache.go",
        "state.go",
        "state-bellatrix.go",
        "trailing_slot_state_cache.go",
        "transition.go",
        "transition_no_verify_sig.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/core/transition",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/blocks:go_default_library",
        "//beacon-chain/core/capella:go_default_library",
        "//beacon-chain/core/deneb:go_default_library",
        "//beacon-chain/core/electra:go_default_library",
        "//beacon-chain/core/epoch:go_default_library",
        "//beacon-chain/core/epoch/precompute:go_default_library",
        "//beacon-chain/core/execution:go_default_library",
        "//beacon-chain/core/fulu:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/core/transition/interop:go_default_library",
        "//beacon-chain/core/validators:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//beacon-chain/state/stateutil:go_default_library",
        "//config/features:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/trie:go_default_library",
        "//crypto/bls:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//monitoring/tracing:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@io_opentelemetry_go_otel_trace//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    size = "small",
    srcs = [
        "altair_transition_no_verify_sig_test.go",
        "bellatrix_transition_no_verify_sig_test.go",
        "benchmarks_test.go",
        "skip_slot_cache_test.go",
        "state_fuzz_test.go",
        "state_test.go",
        "trailing_slot_state_cache_test.go",
        "transition_fuzz_test.go",
        "transition_no_verify_sig_test.go",
        "transition_test.go",
    ],
    data = [
        "//testing/benchmark/benchmark_files:benchmark_data",
    ],
    embed = [":go_default_library"],
    shard_count = 3,
    deps = [
        "//beacon-chain/core/altair:go_default_library",
        "//beacon-chain/core/blocks:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/p2p/types:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/benchmark:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_google_gofuzz//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
