load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "transition.go",
        "upgrade.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/core/fulu",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/core/electra:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/params:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "transition_test.go",
        "upgrade_test.go",
    ],
    deps = [
        ":go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
    ],
)
