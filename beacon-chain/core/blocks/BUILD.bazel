load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "attestation.go",
        "attester_slashing.go",
        "deposit.go",
        "error.go",
        "eth1_data.go",
        "exit.go",
        "genesis.go",
        "header.go",
        "log.go",
        "payload.go",
        "proposer_slashing.go",
        "randao.go",
        "signature.go",
        "withdrawals.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/core/blocks",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/core/validators:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/slice:go_default_library",
        "//container/trie:go_default_library",
        "//contracts/deposit:go_default_library",
        "//crypto/bls:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//encoding/ssz:go_default_library",
        "//math:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//proto/prysm/v1alpha1/slashings:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    size = "medium",
    srcs = [
        "attestation_regression_test.go",
        "attestation_test.go",
        "attester_slashing_test.go",
        "block_operations_fuzz_test.go",
        "block_regression_test.go",
        "deposit_test.go",
        "eth1_data_test.go",
        "exit_test.go",
        "exports_test.go",
        "genesis_test.go",
        "header_test.go",
        "payload_test.go",
        "proposer_slashing_regression_test.go",
        "proposer_slashing_test.go",
        "randao_test.go",
        "signature_test.go",
        "withdrawals_test.go",
    ],
    data = glob(["testdata/**"]),
    embed = [":go_default_library"],
    shard_count = 2,
    deps = [
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/core/validators:go_default_library",
        "//beacon-chain/p2p/types:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/trie:go_default_library",
        "//crypto/bls:go_default_library",
        "//crypto/bls/common:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//encoding/ssz:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//proto/prysm/v1alpha1/attestation/aggregation:go_default_library",
        "//proto/prysm/v1alpha1/attestation/aggregation/attestations:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/fuzz:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_google_gofuzz//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
