load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "das_core.go",
        "info.go",
        "metrics.go",
        "p2p_interface.go",
        "reconstruction.go",
        "validator.go",
        "verification.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/core/peerdas",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/blockchain/kzg:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/trie:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enode:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enr:go_default_library",
        "@com_github_hashicorp_golang_lru//:go_default_library",
        "@com_github_holiman_uint256//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@org_golang_x_sync//errgroup:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "das_core_test.go",
        "info_test.go",
        "p2p_interface_test.go",
        "reconstruction_test.go",
        "utils_test.go",
        "validator_test.go",
        "verification_test.go",
    ],
    deps = [
        ":go_default_library",
        "//beacon-chain/blockchain/kzg:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "@com_github_consensys_gnark_crypto//ecc/bls12-381/fr:go_default_library",
        "@com_github_crate_crypto_go_kzg_4844//:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enode:go_default_library",
        "@com_github_ethereum_go_ethereum//p2p/enr:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@org_golang_x_sync//errgroup:go_default_library",
    ],
)
