package state_native_test

import (
	"context"
	"testing"

	"github.com/OffchainLabs/prysm/v6/beacon-chain/core/helpers"
	state_native "github.com/OffchainLabs/prysm/v6/beacon-chain/state/state-native"
	"github.com/OffchainLabs/prysm/v6/beacon-chain/state/stateutil"
	"github.com/OffchainLabs/prysm/v6/config/params"
	"github.com/OffchainLabs/prysm/v6/consensus-types/primitives"
	ethpb "github.com/OffchainLabs/prysm/v6/proto/prysm/v1alpha1"
	"github.com/OffchainLabs/prysm/v6/testing/require"
	"github.com/OffchainLabs/prysm/v6/testing/util"
)

func TestProposerLookahead(t *testing.T) {
	t.Run("Fulu expected values", func(t *testing.T) {
		lookahead := make([]uint64, int(params.BeaconConfig().MinSeedLookahead+1)*int(params.BeaconConfig().SlotsPerEpoch))
		want := make([]primitives.ValidatorIndex, int(params.BeaconConfig().MinSeedLookahead+1)*int(params.BeaconConfig().SlotsPerEpoch))
		st, err := state_native.InitializeFromProtoFulu(&ethpb.BeaconStateFulu{
			ProposerLookahead: lookahead,
		})
		require.NoError(t, err)
		got, err := st.ProposerLookahead()
		require.NoError(t, err)
		require.Equal(t, len(want), len(got))
		for i, w := range want {
			require.Equal(t, w, got[i], "index %d", i)
		}
	})

	t.Run("Fulu error on invalid size", func(t *testing.T) {
		lookahead := make([]primitives.ValidatorIndex, int(params.BeaconConfig().MinSeedLookahead+1)*int(params.BeaconConfig().SlotsPerEpoch)+1)
		st, err := state_native.InitializeFromProtoFulu(&ethpb.BeaconStateFulu{})
		require.NoError(t, err)
		require.ErrorContains(t, "invalid size for proposer lookahead", st.SetProposerLookahead(lookahead))
	})

	t.Run("earlier than electra returns error", func(t *testing.T) {
		st, err := state_native.InitializeFromProtoDeneb(&ethpb.BeaconStateDeneb{})
		require.NoError(t, err)
		_, err = st.ProposerLookahead()
		require.ErrorContains(t, "is not supported", err)
		lookahead := make([]primitives.ValidatorIndex, int(params.BeaconConfig().MinSeedLookahead+1)*int(params.BeaconConfig().SlotsPerEpoch))
		require.ErrorContains(t, "is not supported", st.SetProposerLookahead(lookahead))
	})
}

// TestProposerLookaheadLengthValidationBypass demonstrates the consensus vulnerability
// where the Fulu upgrade path bypasses ProposerLookahead length validation, allowing
// mis-sized vectors to slip through and cause state root divergence.
func TestProposerLookaheadLengthValidationBypass(t *testing.T) {
	ctx := context.Background()

	// Calculate expected length: (MinSeedLookahead + 1) * SlotsPerEpoch
	expectedLength := int(params.BeaconConfig().MinSeedLookahead+1) * int(params.BeaconConfig().SlotsPerEpoch)

	// Create a pre-Fulu state (Electra) for upgrade testing
	preState, _ := util.DeterministicGenesisStateElectra(t, 64)

	// Test 1: Demonstrate that the setter correctly enforces length validation
	t.Run("Setter enforces length validation (correct behavior)", func(t *testing.T) {
		fuluState, err := state_native.InitializeFromProtoFulu(&ethpb.BeaconStateFulu{})
		require.NoError(t, err)

		// Try to set wrong length - should fail
		wrongLengthLookahead := make([]primitives.ValidatorIndex, expectedLength+5) // 5 extra elements
		err = fuluState.SetProposerLookahead(wrongLengthLookahead)
		require.ErrorContains(t, "invalid size for proposer lookahead", err)

		// Try to set correct length - should succeed
		correctLengthLookahead := make([]primitives.ValidatorIndex, expectedLength)
		err = fuluState.SetProposerLookahead(correctLengthLookahead)
		require.NoError(t, err)
	})

	// Test 2: Demonstrate that SSZ unmarshaling enforces fixed length (64 for mainnet/minimal)
	t.Run("SSZ unmarshaling enforces fixed length", func(t *testing.T) {
		// Create a proto with wrong length
		wrongLengthProto := &ethpb.BeaconStateFulu{
			ProposerLookahead: make([]uint64, expectedLength+10), // Wrong length
		}

		// Marshal to SSZ - this should fail due to length mismatch
		_, err := wrongLengthProto.MarshalSSZ()
		require.ErrorContains(t, "vector length", err) // SSZ enforces exact length of 64
	})

	// Test 3: VULNERABILITY - Demonstrate that upgrade path bypasses validation
	t.Run("VULNERABILITY: Upgrade path bypasses length validation", func(t *testing.T) {
		// Mock a malicious/buggy InitializeProposerLookahead that returns wrong length
		maliciousLookahead := make([]uint64, expectedLength+7) // Wrong length: 71 instead of 64
		for i := range maliciousLookahead {
			maliciousLookahead[i] = uint64(i % 10) // Fill with some validator indices
		}

		// Simulate the upgrade path by manually creating the proto (bypassing InitializeProposerLookahead)
		// This represents what would happen if InitializeProposerLookahead had a bug or config mismatch
		maliciousProto := &ethpb.BeaconStateFulu{
			GenesisTime:           uint64(preState.GenesisTime().Unix()),
			GenesisValidatorsRoot: preState.GenesisValidatorsRoot(),
			Slot:                  preState.Slot(),
			Fork: &ethpb.Fork{
				PreviousVersion: preState.Fork().CurrentVersion,
				CurrentVersion:  params.BeaconConfig().FuluForkVersion,
				Epoch:           0,
			},
			LatestBlockHeader:           preState.LatestBlockHeader(),
			BlockRoots:                  preState.BlockRoots(),
			StateRoots:                  preState.StateRoots(),
			HistoricalRoots:             preState.HistoricalRoots(),
			Eth1Data:                    preState.Eth1Data(),
			Eth1DataVotes:               preState.Eth1DataVotes(),
			Eth1DepositIndex:            preState.Eth1DepositIndex(),
			Validators:                  preState.Validators(),
			Balances:                    preState.Balances(),
			RandaoMixes:                 preState.RandaoMixes(),
			Slashings:                   preState.Slashings(),
			JustificationBits:           preState.JustificationBits(),
			PreviousJustifiedCheckpoint: preState.PreviousJustifiedCheckpoint(),
			CurrentJustifiedCheckpoint:  preState.CurrentJustifiedCheckpoint(),
			FinalizedCheckpoint:         preState.FinalizedCheckpoint(),
			ProposerLookahead:           maliciousLookahead, // WRONG LENGTH - should be 64, is 71
		}

		// The unsafe initializer accepts whatever length comes in - NO VALIDATION
		vulnerableState, err := state_native.InitializeFromProtoUnsafeFulu(maliciousProto)
		require.NoError(t, err) // This succeeds despite wrong length!

		// Verify the state has the wrong length
		lookahead, err := vulnerableState.ProposerLookahead()
		require.NoError(t, err)
		require.Equal(t, expectedLength+7, len(lookahead)) // Wrong length persisted!

		// Now demonstrate the consensus impact: different state roots
		correctLookahead := make([]uint64, expectedLength) // Correct length
		for i := range correctLookahead {
			correctLookahead[i] = uint64(i % 10) // Same values, correct length
		}

		correctProto := &ethpb.BeaconStateFulu{
			GenesisTime:           uint64(preState.GenesisTime().Unix()),
			GenesisValidatorsRoot: preState.GenesisValidatorsRoot(),
			Slot:                  preState.Slot(),
			Fork: &ethpb.Fork{
				PreviousVersion: preState.Fork().CurrentVersion,
				CurrentVersion:  params.BeaconConfig().FuluForkVersion,
				Epoch:           0,
			},
			LatestBlockHeader:           preState.LatestBlockHeader(),
			BlockRoots:                  preState.BlockRoots(),
			StateRoots:                  preState.StateRoots(),
			HistoricalRoots:             preState.HistoricalRoots(),
			Eth1Data:                    preState.Eth1Data(),
			Eth1DataVotes:               preState.Eth1DataVotes(),
			Eth1DepositIndex:            preState.Eth1DepositIndex(),
			Validators:                  preState.Validators(),
			Balances:                    preState.Balances(),
			RandaoMixes:                 preState.RandaoMixes(),
			Slashings:                   preState.Slashings(),
			JustificationBits:           preState.JustificationBits(),
			PreviousJustifiedCheckpoint: preState.PreviousJustifiedCheckpoint(),
			CurrentJustifiedCheckpoint:  preState.CurrentJustifiedCheckpoint(),
			FinalizedCheckpoint:         preState.FinalizedCheckpoint(),
			ProposerLookahead:           correctLookahead, // CORRECT LENGTH
		}

		correctState, err := state_native.InitializeFromProtoUnsafeFulu(correctProto)
		require.NoError(t, err)

		// Compute field roots - they will be different due to length difference
		wrongLengthLookahead, err := vulnerableState.ProposerLookahead()
		require.NoError(t, err)
		correctLengthLookahead, err := correctState.ProposerLookahead()
		require.NoError(t, err)

		wrongRoot, err := stateutil.ProposerLookaheadRoot(wrongLengthLookahead)
		require.NoError(t, err)
		correctRoot, err := stateutil.ProposerLookaheadRoot(correctLengthLookahead)
		require.NoError(t, err)

		// CRITICAL: Different lengths produce different field roots
		require.NotEqual(t, wrongRoot, correctRoot,
			"States with different ProposerLookahead lengths produce different field roots - consensus divergence!")

		// This would lead to different overall state roots and cross-client splits
		t.Logf("Wrong length (%d) field root: %x", len(wrongLengthLookahead), wrongRoot)
		t.Logf("Correct length (%d) field root: %x", len(correctLengthLookahead), correctRoot)
	})

	// Test 4: Show that the real InitializeProposerLookahead produces correct length (today)
	t.Run("Current InitializeProposerLookahead produces correct length", func(t *testing.T) {
		// This shows that today's implementation is correct, but the upgrade path doesn't validate it
		lookahead, err := helpers.InitializeProposerLookahead(ctx, preState, 0)
		require.NoError(t, err)
		require.Equal(t, expectedLength, len(lookahead),
			"InitializeProposerLookahead should produce exactly %d elements", expectedLength)
	})
}
