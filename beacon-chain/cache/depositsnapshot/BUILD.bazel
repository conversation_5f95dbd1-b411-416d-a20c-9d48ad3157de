load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "deposit_fetcher.go",
        "deposit_inserter.go",
        "deposit_pruner.go",
        "deposit_tree.go",
        "deposit_tree_snapshot.go",
        "merkle_tree.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/cache/depositsnapshot",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/cache:go_default_library",
        "//config/fieldparams:go_default_library",
        "//container/slice:go_default_library",
        "//container/trie:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//math:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_wealdtech_go_bytesutil//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "deposit_cache_test.go",
        "deposit_fetcher_test.go",
        "deposit_pruner_test.go",
        "deposit_tree_snapshot_test.go",
        "merkle_tree_test.go",
        "spec_test.go",
    ],
    data = [
        "@eip4881_spec_tests//:test_data",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/cache:go_default_library",
        "//config/params:go_default_library",
        "//container/trie:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//io/file:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@in_gopkg_yaml_v3//:go_default_library",
        "@io_bazel_rules_go//go/tools/bazel:go_default_library",
    ],
)
