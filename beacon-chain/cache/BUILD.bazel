load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "active_balance.go",
        "active_balance_disabled.go",  # keep
        "attestation.go",
        "attestation_data.go",
        "balance_cache_key.go",
        "checkpoint_state.go",
        "committee.go",
        "committee_disabled.go",  # keep
        "committees.go",
        "common.go",
        "doc.go",
        "error.go",
        "interfaces.go",
        "payload_id.go",
        "proposer_indices.go",
        "proposer_indices_disabled.go",  # keep
        "proposer_indices_type.go",
        "registration.go",
        "skip_slot_cache.go",
        "subnet_ids.go",
        "sync_committee.go",
        "sync_committee_disabled.go",  # keep
        "sync_committee_head_state.go",
        "sync_subnet_ids.go",
        "tracked_validators.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/cache",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//testing/spectest:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//beacon-chain/forkchoice/types:go_default_library",
        "//beacon-chain/operations/attestations/attmap:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//cache/lru:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/slice:go_default_library",
        "//crypto/bls:go_default_library",
        "//crypto/hash:go_default_library",
        "//crypto/rand:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//math:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//runtime/version:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_hashicorp_golang_lru//:go_default_library",
        "@com_github_patrickmn_go_cache//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@io_k8s_client_go//tools/cache:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    size = "small",
    srcs = [
        "active_balance_test.go",
        "attestation_data_test.go",
        "attestation_test.go",
        "cache_test.go",
        "checkpoint_state_test.go",
        "committee_fuzz_test.go",
        "committee_test.go",
        "payload_id_test.go",
        "private_access_test.go",
        "proposer_indices_test.go",
        "registration_test.go",
        "skip_slot_cache_test.go",
        "subnet_ids_test.go",
        "sync_committee_head_state_test.go",
        "sync_committee_test.go",
        "sync_subnet_ids_test.go",
        "tracked_validators_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/forkchoice/types:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls/blst:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_google_gofuzz//:go_default_library",
        "@com_github_hashicorp_golang_lru//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_stretchr_testify//require:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
