load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "availability_blobs.go",
        "blob_cache.go",
        "data_column_cache.go",
        "iface.go",
        "mock.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/das",
    visibility = ["//visibility:public"],
    deps = [
        "//beacon-chain/db/filesystem:go_default_library",
        "//beacon-chain/verification:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//runtime/logging:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "availability_blobs_test.go",
        "blob_cache_test.go",
        "data_column_cache_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/db/filesystem:go_default_library",
        "//beacon-chain/verification:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
    ],
)
