load("@prysm//tools/go:def.bzl", "go_library")

go_library(
    name = "go_default_library",
    srcs = ["types.go"],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/forkchoice/types",
    visibility = ["//visibility:public"],
    deps = [
        "//config/fieldparams:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
    ],
)
