load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "doc.go",
        "errors.go",
        "forkchoice.go",
        "last_root.go",
        "metrics.go",
        "node.go",
        "on_tick.go",
        "optimistic_sync.go",
        "proposer_boost.go",
        "reorg_late_blocks.go",
        "store.go",
        "types.go",
        "unrealized_justification.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/forkchoice/doubly-linked-tree",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//testing/spectest:__subpackages__",
    ],
    deps = [
        "//beacon-chain/core/epoch/precompute:go_default_library",
        "//beacon-chain/forkchoice:go_default_library",
        "//beacon-chain/forkchoice/types:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/forkchoice:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "ffg_update_test.go",
        "forkchoice_test.go",
        "last_root_test.go",
        "no_vote_test.go",
        "node_test.go",
        "on_tick_test.go",
        "optimistic_sync_test.go",
        "proposer_boost_test.go",
        "reorg_late_blocks_test.go",
        "store_test.go",
        "unrealized_justification_test.go",
        "vote_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/forkchoice:go_default_library",
        "//beacon-chain/forkchoice/types:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/forkchoice:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "//time/slots:go_default_library",
    ],
)
