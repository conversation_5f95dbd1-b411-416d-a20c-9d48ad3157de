load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "log.go",
        "metrics.go",
        "pool.go",
        "prepare_forkchoice.go",
        "prune_expired.go",
        "service.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/operations/attestations",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//testing/spectest:__subpackages__",
    ],
    deps = [
        "//beacon-chain/cache:go_default_library",
        "//beacon-chain/operations/attestations/kv:go_default_library",
        "//cache/lru:go_default_library",
        "//config/features:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//proto/prysm/v1alpha1/attestation/aggregation/attestations:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_hashicorp_golang_lru//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "pool_test.go",
        "prepare_forkchoice_test.go",
        "prune_expired_test.go",
        "service_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//async:go_default_library",
        "//beacon-chain/operations/attestations/kv:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation/aggregation/attestations:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)
