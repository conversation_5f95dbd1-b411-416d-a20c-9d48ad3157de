load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "aggregated.go",
        "block.go",
        "kv.go",
        "seen_bits.go",
        "unaggregated.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/operations/attestations/kv",
    visibility = ["//beacon-chain:__subpackages__"],
    deps = [
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/operations/attestations/attmap:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//proto/prysm/v1alpha1/attestation/aggregation/attestations:go_default_library",
        "//runtime/version:go_default_library",
        "@com_github_patrickmn_go_cache//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "aggregated_test.go",
        "block_test.go",
        "forkchoice_test.go",
        "seen_bits_test.go",
        "unaggregated_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//config/fieldparams:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
        "@com_github_patrickmn_go_cache//:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prysmaticlabs_go_bitfield//:go_default_library",
    ],
)
