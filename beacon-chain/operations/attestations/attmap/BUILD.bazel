load("@prysm//tools/go:def.bzl", "go_library")

go_library(
    name = "go_default_library",
    srcs = ["map.go"],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/operations/attestations/attmap",
    visibility = ["//visibility:public"],
    deps = [
        "//proto/prysm/v1alpha1:go_default_library",
        "//proto/prysm/v1alpha1/attestation:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
    ],
)
