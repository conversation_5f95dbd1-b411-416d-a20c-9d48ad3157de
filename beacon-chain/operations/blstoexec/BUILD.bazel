load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "doc.go",
        "pool.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/operations/blstoexec",
    visibility = [
        "//beacon-chain:__subpackages__",
    ],
    deps = [
        "//beacon-chain/core/blocks:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/doubly-linked-list:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    size = "small",
    srcs = ["pool_test.go"],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/core/signing:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/state/state-native:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls:go_default_library",
        "//crypto/bls/common:go_default_library",
        "//crypto/hash:go_default_library",
        "//encoding/ssz:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
    ],
)
