load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "doc.go",
        "log.go",
        "metrics.go",
        "pool.go",
        "service.go",
        "types.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/beacon-chain/operations/slashings",
    visibility = [
        "//beacon-chain:__subpackages__",
        "//testing/endtoend:__subpackages__",
        "//testing/slasher/simulator:__pkg__",
    ],
    deps = [
        "//beacon-chain/core/blocks:go_default_library",
        "//beacon-chain/core/helpers:go_default_library",
        "//beacon-chain/core/time:go_default_library",
        "//beacon-chain/startup:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//container/slice:go_default_library",
        "//monitoring/tracing/trace:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//time/slots:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
        "@com_github_trailofbits_go_mutexasserts//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    size = "small",
    srcs = [
        "service_attester_test.go",
        "service_new_test.go",
        "service_proposer_test.go",
        "service_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//beacon-chain/operations/slashings/mock:go_default_library",
        "//beacon-chain/startup:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//crypto/bls:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "//testing/util:go_default_library",
    ],
)
