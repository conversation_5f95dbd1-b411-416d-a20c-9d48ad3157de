load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "event_stream.go",
        "utils.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/api/client/event",
    visibility = ["//visibility:public"],
    deps = [
        "//api:go_default_library",
        "//api/client:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "event_stream_test.go",
        "utils_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//testing/require:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)
