load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "client.go",
        "doc.go",
        "log.go",
        "template.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/api/client/beacon",
    visibility = ["//visibility:public"],
    deps = [
        "//api/client:go_default_library",
        "//api/server:go_default_library",
        "//api/server/structs:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = ["client_test.go"],
    embed = [":go_default_library"],
    deps = [
        "//api/client:go_default_library",
        "//testing/require:go_default_library",
    ],
)
