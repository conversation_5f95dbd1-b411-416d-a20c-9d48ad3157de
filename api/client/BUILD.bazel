load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "client.go",
        "errors.go",
        "options.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/api/client",
    visibility = ["//visibility:public"],
    deps = ["@com_github_pkg_errors//:go_default_library"],
)

go_test(
    name = "go_default_test",
    srcs = ["client_test.go"],
    embed = [":go_default_library"],
    deps = ["//testing/require:go_default_library"],
)
