load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "block.go",
        "block_execution.go",
        "conversions.go",
        "conversions_blob.go",
        "conversions_block.go",
        "conversions_block_execution.go",
        "conversions_lightclient.go",
        "conversions_state.go",
        "endpoints_beacon.go",
        "endpoints_blob.go",
        "endpoints_builder.go",
        "endpoints_config.go",
        "endpoints_debug.go",
        "endpoints_events.go",
        "endpoints_lightclient.go",
        "endpoints_node.go",
        "endpoints_rewards.go",
        "endpoints_validator.go",
        "other.go",
        "state.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/api/server/structs",
    visibility = ["//visibility:public"],
    deps = [
        "//api/server:go_default_library",
        "//beacon-chain/state:go_default_library",
        "//config/fieldparams:go_default_library",
        "//config/params:go_default_library",
        "//consensus-types/blocks:go_default_library",
        "//consensus-types/interfaces:go_default_library",
        "//consensus-types/primitives:go_default_library",
        "//consensus-types/validator:go_default_library",
        "//container/slice:go_default_library",
        "//crypto/bls:go_default_library",
        "//encoding/bytesutil:go_default_library",
        "//math:go_default_library",
        "//proto/engine/v1:go_default_library",
        "//proto/eth/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//runtime/version:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "conversions_block_execution_test.go",
        "conversions_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//proto/engine/v1:go_default_library",
        "//proto/prysm/v1alpha1:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "@com_github_ethereum_go_ethereum//common:go_default_library",
        "@com_github_ethereum_go_ethereum//common/hexutil:go_default_library",
    ],
)
