load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "middleware.go",
        "util.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/api/server/middleware",
    visibility = ["//visibility:public"],
    deps = [
        "//api:go_default_library",
        "//api/apiutil:go_default_library",
        "@com_github_rs_cors//:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = [
        "middleware_test.go",
        "util_test.go",
    ],
    embed = [":go_default_library"],
    deps = [
        "//api:go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "@com_github_sirupsen_logrus//:go_default_library",
    ],
)
