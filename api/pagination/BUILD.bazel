load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = ["pagination.go"],
    importpath = "github.com/OffchainLabs/prysm/v6/api/pagination",
    visibility = ["//visibility:public"],
    deps = [
        "//config/params:go_default_library",
        "@com_github_pkg_errors//:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = ["pagination_test.go"],
    deps = [
        ":go_default_library",
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
    ],
)
