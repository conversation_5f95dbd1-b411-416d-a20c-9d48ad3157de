load("@prysm//tools/go:def.bzl", "go_library", "go_test")

go_library(
    name = "go_default_library",
    srcs = [
        "grpcutils.go",
        "parameters.go",
    ],
    importpath = "github.com/OffchainLabs/prysm/v6/api/grpc",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_sirupsen_logrus//:go_default_library",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//metadata:go_default_library",
    ],
)

go_test(
    name = "go_default_test",
    srcs = ["grpcutils_test.go"],
    embed = [":go_default_library"],
    deps = [
        "//testing/assert:go_default_library",
        "//testing/require:go_default_library",
        "@com_github_sirupsen_logrus//hooks/test:go_default_library",
        "@org_golang_google_grpc//metadata:go_default_library",
    ],
)
