name: Horusec Security Scan

on:
  schedule:
    # Runs cron at 16.00 UTC on
    - cron: '0 0 * * SUN'

jobs:
  Horus<PERSON>_Scan:
    name: horusec-Scan
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
    - name: Check out code
      uses: actions/checkout@v2
      with: # Required when commit authors is enabled
        fetch-depth: 0
        
    - name: Running Security Scan
      run: |
        curl -fsSL https://raw.githubusercontent.com/ZupIT/horusec/main/deployments/scripts/install.sh | bash -s latest
        horusec start -t="10000" -p="./" -e="true" -i="**/crypto/bls/herumi/**, **/**/*_test.go, **/third_party/afl/**, **/crypto/keystore/key.go"