# This workflow will build a golang project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-go

name: changelog

on:
  pull_request:
    branches: [ "develop" ]

jobs:
  run-changelog-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source code
        uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3.6.0

      - name: Download unclog binary
        uses: dsaltares/fetch-gh-release-asset@aa2ab1243d6e0d5b405b973c89fa4d06a2d0fff7 # 1.1.2
        with:
          repo: OffchainLabs/unclog
          version: "tags/v0.1.5"
          file: "unclog"

      - name: Get new changelog files
        id: new-changelog-files
        uses: OffchainLabs/gh-action-changed-files@9200e69727eb73eb060652b19946b8a2fdfb654b # v4.0.8
        with:
          files: |
            changelog/**.md

      - name: Run lint command
        env:
          ALL_ADDED_MARKDOWN: ${{ steps.new-changelog-files.outputs.added_files }}
        run: chmod +x unclog && ./unclog check -fragment-env=ALL_ADDED_MARKDOWN
