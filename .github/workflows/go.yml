name: Go

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ '*' ]
  merge_group:
    types: [checks_requested]

jobs:
  formatting:
    name: Formatting
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Go mod tidy checker
        id: gomodtidy
        uses: ./.github/actions/gomodtidy

  gosec:
    name: Gosec scan
    runs-on: ubuntu-latest
    env:
      GO111MODULE: on
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Go 1.24
        uses: actions/setup-go@v4
        with:
          go-version: '1.24.0'
      - name: Run Gosec Security Scanner
        run: | # https://github.com/securego/gosec/issues/469
          export PATH=$PATH:$(go env GOPATH)/bin
          go install github.com/securego/gosec/v2/cmd/gosec@v2.22.1
          gosec -exclude-generated -exclude=G307,G115 -exclude-dir=crypto/bls/herumi ./...

  lint:
    name: <PERSON>t
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Go 1.24
        uses: actions/setup-go@v4
        with:
          go-version: '1.24.0'
          id: go

      - name: Golangci-lint
        uses: golangci/golangci-lint-action@v5
        with:
          version: v1.64.5
          args: --config=.golangci.yml --out-${NO_FUTURE}format colored-line-number

  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Set up Go 1.x
        uses: actions/setup-go@v4
        with:
          go-version: '1.24.0'
        id: go

      - name: Check out code into the Go module directory
        uses: actions/checkout@v4

      - name: Get dependencies
        run: |
          go get -v -t -d ./...

      - name: Build
        # Use blst tag to allow go and bazel builds for blst.
        run: go build -v ./...
        env:
           CGO_CFLAGS: "-O2 -D__BLST_PORTABLE__"
        # fuzz leverage go tag based stubs at compile time.
        # Building and testing with these tags should be checked and enforced at pre-submit.
      - name: Test for fuzzing
        run: go test  -tags=fuzz,develop ./...  -test.run=^Fuzz
        env:
           CGO_CFLAGS: "-O2 -D__BLST_PORTABLE__"

# Tests run via Bazel for now...
#      - name: Test
#        run: go test -v ./...
