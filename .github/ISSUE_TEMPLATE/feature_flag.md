---
name: "\U0001F984Feature Flag Tracking"
about: Track a new feature, in development, in Prysm. This issue template should only be used by developers or contributors!
labels: Tracking
---
<!--💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎

Hellooo! 😄 

Thanks for taking the time to file a tracking issue for your new feature. These issues really help
us track progress of features as they work their way through development. Be sure to review 
shared/featureconfig/README.md for the latest documentation around feature flags.

💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎💎-->

# 🦄 Feature Tracking

**Current status** (opt-in or opt-out): 

**Opt-in feature flag name**:

**Opt-out feature flag name**: 

### Feature Description

<!-- ✍️--> A clear and concise description of the new feature. Provide links to your technical
design document or other supporting information.

### Tasks

- [ ] Feature flag created as opt-in
- [ ] Feature implemented / code complete
- [ ] Feature tested in production for adequate amount of time
- [ ] Feature flag is inverted to be opt-out and the opt-in flag is deprecated
- [ ] Feature has made it to a tagged production release as an opt-out flag
- [ ] Opt-out feature flag is deprecated and old code paths are cleaned up

This issue should be closed after all of the above tasks are complete.
